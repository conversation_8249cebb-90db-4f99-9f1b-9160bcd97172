<?php

namespace App\Traits\Passport;

use App\Models\Passport\Client;
use Illuminate\Support\Facades\Session;

trait PassportRedirectTrait
{
    public const DEFAULT_CLIENT_NAME = 'GravityWriteDefaultRedirect';

    /**
     * Set the default redirection when the user is not authenticated
     *
     * It looks for a client named "GravityWriteDefaultRedirect" and uses its first redirect URI
     * to build the default redirection URL.
     *
     * @return void
     */
    public function setDefaultRedirection(): void
    {
        if (request()->session()->has('redirect_uri')) {
            return;
        }

        $client = Client::where('name', self::DEFAULT_CLIENT_NAME)->first();
        $redirectTo = explode(',', (string) $client->redirect)[0] ?? '';
        $fields = [
            'client_id' => $client->id,
            'redirect_uri' => trim($redirectTo),
            'scope' => '',
            'response_type' => 'code',
        ];

        $query = http_build_query($fields);

        request()->session()->put('authRequest', $fields);
    }

    /**
     * Returns the parameters for the redirect URL if the redirect URL is for registration.
     * Now includes email existence checking to determine proper flow.
     *
     * @return array|null|string Returns array with email for registration, 'login' for existing users, or false
     */
    public function isRegister()
    {
        if (session()->has('redirect_uri') && session('is_first_time')) {
            session()->put('is_first_time', false);

            $redirectUrl = Session::get('redirect_uri');
            $parsedUrl = parse_url((string) $redirectUrl);

            $params = [];
            parse_str(@$parsedUrl['query'], $params);
            if (isset($params['is_register']) && $params['is_register']) {
                $parameters = [];
                if (isset($params['email'])) {
                    $email = $params['email'];
                    $parameters['email'] = $email;

                    // Check if user exists for is_register=1 flows
                    if ($this->checkEmailExists($email)) {
                        // User exists - redirect to login with pre-filled email
                        session()->put('prefill_email', $email);
                        return 'login';
                    }

                    // User doesn't exist - proceed with registration
                    return $parameters;
                } else {
                    // No email parameter - redirect to registration page
                    return [];
                }
            }
        }

        if (request()->has('email')) {
            return request()->only(['email']);
        }

        return false;
    }

    /**
     * Check if an email address exists in the users table.
     *
     * @param  string  $email
     * @return bool
     */
    public function checkEmailExists(string $email): bool
    {
        return \App\Models\User::where('email', $email)->exists();
    }

    /**
     * Clean up session data to prevent interference between flows.
     * This ensures proper session management across browser tab closures.
     *
     * @return void
     */
    public function cleanupSessionData(): void
    {
        // Remove specific session keys that might interfere with new flows
        session()->forget(['prefill_email', 'registration_flow', 'login_flow', 'google_sso_email']);
    }

    /**
     * Check if the given email address is a Gmail address for registration flows.
     * Enhanced to handle both login and registration scenarios.
     *
     * @param  string  $email
     * @param  bool  $isRegistration Whether this is for a registration flow
     * @return bool
     */
    public function isGmailForFlow(string $email, bool $isRegistration = false): bool
    {
        $isGmail = $this->isGmail($email);

        if ($isGmail && $isRegistration) {
            // For registration flows, store the email for Google SSO
            session()->put('google_sso_email', $email);
        }

        return $isGmail;
    }

    /**
     * Check if the given email address is a Gmail address.
     *
     * @param  string  $email
     * @return bool
     */
    public function isGmail(string $email): bool
    {
        return str_contains($email, '@gmail.com');
    }

    /**
     * Stores the redirect URI for the current request in the session.
     *
     * @return void
     */
    protected function setRedirectUri(): void
    {
        if (request()->session()->has('redirect_uri')) {
            return;
        }

        if (@request()->session()->get('url')['intended']) {
            request()->session()->put('redirect_uri', request()->session()->get('url')['intended']);
        }

        $previousUrl = request()->session()->previousUrl();
        $parsedUrl = parse_url((string) $previousUrl);

        $params = [];
        parse_str(@$parsedUrl['query'], $params);

        if (isset($params['redirect_uri'])) {
            $client = Client::where('id', $params['client_id'])
                ->whereRaw("FIND_IN_SET('{$params['redirect_uri']}', redirect) > 0")
                ->first();

            if ($client) {
                request()->session()->put('redirect_uri', $previousUrl);
            }
        }

        $this->setDefaultRedirection();
    }
}
